import { CandlestickChart } from '@/components/chart/candlestick-chart'
import { useGetMarginTradeKline } from '@/queries/kline'
import type { IKLineInfo } from '@/types/kline'
import { ResolutionKey, BAR_DURATION_MAP } from '@/components/chart/types'
import { useState, useMemo, useCallback, useEffect, useRef } from 'react'
import type {
  UTCTimestamp,
  LogicalRange,
  CandlestickData
} from 'lightweight-charts'
import KLineService from '@/services/kline'
import type { IChartApi } from 'lightweight-charts'
import { SUI_COIN_TYPE } from '@/lib/coin'

interface ChartProps {
  realtimeEnabled?: boolean
  realtimeInterval?: number
  coinType?: string
}

export function Chart({
  realtimeEnabled = false,
  realtimeInterval = 60000,
  coinType = SUI_COIN_TYPE
}: ChartProps) {
  const [bar, setBar] = useState<ResolutionKey>(ResolutionKey.Min15)
  const initialSize = 300
  const fetchSize = initialSize + 1
  const additionalSize = 50
  const [historicalData, setHistoricalData] = useState<IKLineInfo[]>([])
  const lastFromTime = useRef<number>(0)
  const isLoadingMore = useRef<boolean>(false)
  const hasMoreData = useRef<boolean>(false)
  const chartRef = useRef<IChartApi | null>(null)
  const seriesRef = useRef<{
    update: (data: CandlestickData<UTCTimestamp>) => void
  } | null>(null)
  const realtimeIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastUpdateTime = useRef<number>(0)
  const visibleRangeRef = useRef<{ from: number; to: number } | null>(null)

  // 启动实时更新
  const startRealtimeUpdates = useCallback(() => {
    // 清除之前的定时器
    if (realtimeIntervalRef.current) {
      clearInterval(realtimeIntervalRef.current)
      realtimeIntervalRef.current = null
    }

    if (realtimeEnabled && seriesRef.current) {
      realtimeIntervalRef.current = setInterval(async () => {
        try {
          // 获取最新的K线数据
          const now = Date.now()
          const fromTime = now - BAR_DURATION_MAP[bar] * 2 // 获取最近2个周期的数据

          // debugger;
          const result = await KLineService.getMarginTradeKline.call({
            address: coinType,
            bar,
            fromTime,
            toTime: now,
            size: 10 // 只获取少量最新数据
          })

          if (result?.data && result.data.length > 0) {
            const latestData = result.data[result.data.length - 1]

            // 检查是否是新数据
            if (latestData.t > lastUpdateTime.current) {
              lastUpdateTime.current = latestData.t

              // 更新图表数据
              const newCandleData: CandlestickData<UTCTimestamp> = {
                time: (latestData.t / 1000) as UTCTimestamp,
                open: latestData.o,
                high: latestData.h,
                low: latestData.l,
                close: latestData.c
              }

              // 使用 series.update() 更新最新K线
              if (seriesRef.current) {
                seriesRef.current.update(newCandleData)
              }
            }
          }
        } catch (error) {
          console.error('Failed to fetch realtime data:', error)
        }
      }, realtimeInterval)
    }
  }, [realtimeEnabled, realtimeInterval, bar, coinType])

  // 当实时更新开关或参数变化时，重新启动定时器
  useEffect(() => {
    startRealtimeUpdates()

    return () => {
      if (realtimeIntervalRef.current) {
        clearInterval(realtimeIntervalRef.current)
        realtimeIntervalRef.current = null
      }
    }
  }, [startRealtimeUpdates])

  useEffect(() => {
    setHistoricalData([])
    lastFromTime.current = 0
    isLoadingMore.current = false
    hasMoreData.current = true
    chartRef.current = null
    seriesRef.current = null
    lastUpdateTime.current = 0
  }, [bar])

  const { toTime, fromTime } = useMemo(() => {
    const toTime = Date.now()
    const fromTime = toTime - initialSize * BAR_DURATION_MAP[bar]
    return { toTime, fromTime }
  }, [bar])

  const { data } = useGetMarginTradeKline(
    {
      address: coinType,
      bar,
      fromTime,
      toTime,
      size: fetchSize
    },
    {
      enabled: !!coinType // 只有当 coinType 有值时才启用查询
    }
  )

  const loadMoreHistoricalData = useCallback(async () => {
    if (isLoadingMore.current || !hasMoreData.current) return

    isLoadingMore.current = true
    if (lastFromTime.current === 0) {
      lastFromTime.current = fromTime
    }
    const historicalFromTime =
      lastFromTime.current - additionalSize * BAR_DURATION_MAP[bar]

    try {
      // debugger;
      const result = await KLineService.getMarginTradeKline.call({
        address: coinType,
        bar,
        fromTime: historicalFromTime,
        toTime: lastFromTime.current,
        size: additionalSize
      })
      lastFromTime.current = historicalFromTime
      if (result && result.data && result.data.length > 0) {
        const currentVisibleRange = chartRef.current
          ?.timeScale()
          .getVisibleLogicalRange()
        if (currentVisibleRange) {
          visibleRangeRef.current = {
            from: currentVisibleRange.from + result.data.length,
            to: currentVisibleRange.to + result.data.length
          }
        }

        setHistoricalData((prev) => [...(result.data || []), ...prev])

        if (result.data.length < additionalSize) {
          hasMoreData.current = false
        }
      } else {
        hasMoreData.current = false
      }
    } catch (error) {
      console.error('Failed to load historical data:', error)
      hasMoreData.current = false
    } finally {
      isLoadingMore.current = false
    }
  }, [bar, fromTime, coinType, additionalSize])

  const handleVisibleRangeChange = useCallback(
    (logicalRange: LogicalRange) => {
      if (
        Number(logicalRange.from) < 10 &&
        !isLoadingMore.current &&
        hasMoreData.current
      ) {
        loadMoreHistoricalData()
      }
    },
    [loadMoreHistoricalData]
  )

  const chartData = useMemo((): CandlestickData<UTCTimestamp>[] => {
    const allData = [...historicalData, ...(data?.data || [])]
    return allData.map((item: IKLineInfo) => ({
      time: (item.t / 1000) as UTCTimestamp,
      open: item.o,
      high: item.h,
      low: item.l,
      close: item.c
    }))
  }, [data?.data, historicalData])

  useEffect(() => {
    if (visibleRangeRef.current && chartRef.current) {
      setTimeout(() => {
        chartRef.current
          ?.timeScale()
          .setVisibleLogicalRange(visibleRangeRef.current!)
        visibleRangeRef.current = null
      }, 0)
    }
  }, [historicalData])

  return (
    <CandlestickChart
      data={chartData}
      currentResolution={bar}
      onResolutionChange={setBar}
      onVisibleRangeChange={handleVisibleRangeChange}
      onChartReady={(chart) => {
        chartRef.current = chart
      }}
      onSeriesReady={(series) => {
        seriesRef.current = series
      }}
      isLoadingMore={isLoadingMore.current}
      className="border border-border-8 rounded-[10px] w-[936px] h-[400px] flex flex-col items-center justify-center p-4"
    />
  )
}
